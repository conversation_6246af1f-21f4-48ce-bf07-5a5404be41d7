import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getNoteByIdAndUserId, analyzeNoteInBackground } from '@/lib/data/notes'
import { DatabaseError } from '@/lib/data/types'

interface RouteParams {
  params: {
    id: string
  }
}

export async function POST(_request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Необходима авторизация' },
        { status: 401 }
      )
    }

    const noteId = params.id

    if (!noteId) {
      return NextResponse.json(
        { error: 'ID заметки обязателен' },
        { status: 400 }
      )
    }

    // Get the note to verify ownership and get content
    const note = await getNoteByIdAndUserId(noteId, user.id)

    if (!note) {
      return NextResponse.json(
        { error: 'Заметка не найдена' },
        { status: 404 }
      )
    }

    // Check if OpenRouter API key is available
    if (!process.env.OPENROUTER_API_KEY) {
      return NextResponse.json(
        { error: 'AI анализ недоступен' },
        { status: 503 }
      )
    }

    // Trigger AI analysis in background
    const success = await analyzeNoteInBackground(
      noteId,
      note.content,
      note.content_type,
      note.attachment_url || undefined
    )

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'AI анализ запущен'
      })
    } else {
      return NextResponse.json(
        { error: 'Не удалось запустить AI анализ' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error in POST /api/notes/[id]/reanalyze:', error)

    if (error instanceof DatabaseError) {
      return NextResponse.json(
        { error: 'Ошибка базы данных' },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { error: 'Внутренняя ошибка сервера' },
      { status: 500 }
    )
  }
}
