import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { CreateNoteRequest, NotesResponse, CreateNoteResponse } from '@/types/notes'
import { getAllNotesByUserId, createNote, analyzeNoteInBackground } from '@/lib/data/notes'
import { DatabaseError } from '@/lib/data/types'
import { determineContentType } from '@/lib/utils/content-type'

export async function GET() {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const notes = await getAllNotesByUserId(user.id)
    return NextResponse.json({ notes } as NotesResponse)
  } catch (error) {
    console.error('Error in GET /api/notes:', error)

    if (error instanceof DatabaseError) {
      return NextResponse.json({ error: 'Failed to fetch notes' }, { status: 500 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { content, content_type, attachment_url }: CreateNoteRequest = await request.json()

    if (!content || !content.trim()) {
      return NextResponse.json({ error: 'Content is required' }, { status: 400 })
    }

    const trimmedContent = content.trim()

    // Determine content type (explicit or auto-detected)
    const finalContentType = determineContentType(trimmedContent, content_type)

    const note = await createNote(user.id, trimmedContent, finalContentType, attachment_url)

    // Trigger AI analysis for text, link content, and notes with attachments
    if (process.env.OPENROUTER_API_KEY && (finalContentType === 'text' || finalContentType === 'link' || attachment_url)) {
      analyzeNoteInBackground(note.id, trimmedContent, finalContentType, attachment_url)
    }

    return NextResponse.json({ note } as CreateNoteResponse, { status: 201 })
  } catch (error) {
    console.error('Error in POST /api/notes:', error)

    if (error instanceof DatabaseError) {
      return NextResponse.json({ error: 'Failed to create note' }, { status: 500 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}


