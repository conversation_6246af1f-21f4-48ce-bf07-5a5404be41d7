import { NextRequest } from 'next/server'
import { generateUploadUrl } from '@/lib/storage/upload'
import { requireAuth } from '@/lib/api/auth-middleware'
import { ApiResponses, handleApiError } from '@/lib/api/responses'
import { z } from 'zod'

// Request validation schema
const uploadUrlRequestSchema = z.object({
  fileName: z.string().min(1, 'Имя файла обязательно'),
  fileSize: z.number().positive('Размер файла должен быть положительным'),
  fileType: z.string().min(1, 'Тип файла обязателен')
})

export type UploadUrlRequest = z.infer<typeof uploadUrlRequestSchema>

export interface UploadUrlResponse {
  uploadUrl: string
  filePath: string
  publicUrl: string
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth()
    if (!authResult.success) {
      return authResult.response
    }

    const user = authResult.user

    // Parse and validate request body
    const body = await request.json()
    const validation = uploadUrlRequestSchema.safeParse(body)

    if (!validation.success) {
      return ApiResponses.badRequest('Неверные данные запроса')
    }

    const { fileName, fileSize, fileType } = validation.data

    // Generate upload URL
    const result = await generateUploadUrl(user.id, {
      name: fileName,
      size: fileSize,
      type: fileType
    })

    if ('error' in result) {
      return ApiResponses.badRequest(result.error)
    }

    return ApiResponses.success(result as UploadUrlResponse)
  } catch (error) {
    return handleApiError(error)
  }
}
