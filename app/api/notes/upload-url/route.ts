import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { generateUploadUrl } from '@/lib/storage/upload'
import { z } from 'zod'

// Request validation schema
const uploadUrlRequestSchema = z.object({
  fileName: z.string().min(1, 'Имя файла обязательно'),
  fileSize: z.number().positive('Размер файла должен быть положительным'),
  fileType: z.string().min(1, 'Тип файла обязателен')
})

export type UploadUrlRequest = z.infer<typeof uploadUrlRequestSchema>

export interface UploadUrlResponse {
  uploadUrl: string
  filePath: string
  publicUrl: string
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Необходима авторизация' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validation = uploadUrlRequestSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Неверные данные запроса', details: validation.error.errors },
        { status: 400 }
      )
    }

    const { fileName, fileSize, fileType } = validation.data

    // Generate upload URL
    const result = await generateUploadUrl(user.id, {
      name: fileName,
      size: fileSize,
      type: fileType
    })

    if ('error' in result) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result as UploadUrlResponse)
  } catch (error) {
    console.error('Error in POST /api/notes/upload-url:', error)
    return NextResponse.json(
      { error: 'Внутренняя ошибка сервера' },
      { status: 500 }
    )
  }
}
