import { generateObject } from 'ai'
import { getModel, DEFAULT_MODEL, MULTIMODAL_MODEL } from './openrouter'
import { ANALYZE_NOTE_PROMPT } from './prompts'
import { replacePlaceholders, AI_CONSTANTS } from './utils'
import { aiLogger } from './logger'
import { analyzeResultSchema, type AnalyzeResult } from './schemas'

export async function analyzeNote(content: string, imageUrl?: string): Promise<AnalyzeResult> {
  // Use multimodal model if image is present, otherwise use default model
  const modelToUse = imageUrl ? MULTIMODAL_MODEL : DEFAULT_MODEL

  const logContext = {
    operation: 'analyze' as const,
    model: modelToUse,
    temperature: AI_CONSTANTS.ANALYZE_TEMPERATURE
  }

  try {


    // For empty content with image, use special prompt
    const noteContent = content || (imageUrl ? '[Изображение без текста]' : '')

    const prompt = replacePlaceholders(ANALYZE_NOTE_PROMPT, {
      noteContent
    })

    aiLogger.logRequest(logContext, prompt)

    // Prepare messages for multimodal analysis
    const messages = []

    if (imageUrl) {
      // Multimodal analysis with image
      messages.push({
        role: 'user' as const,
        content: [
          { type: 'text' as const, text: prompt },
          { type: 'image' as const, image: imageUrl }
        ]
      })
    } else {
      // Text-only analysis - skip if content is empty
      if (!content || content.trim().length === 0) {
        throw new Error('No content to analyze')
      }
      messages.push({
        role: 'user' as const,
        content: prompt
      })
    }

    const { object } = await generateObject({
      model: getModel(modelToUse),
      messages,
      temperature: AI_CONSTANTS.ANALYZE_TEMPERATURE,
      schema: analyzeResultSchema,
      maxTokens: 1000
    })

    aiLogger.logResponse(logContext, object)

    const finalResult = {
      tags: object.tags.slice(0, AI_CONSTANTS.MAX_TAGS_COUNT),
      summary: object.summary
    }

    aiLogger.logResult(logContext, finalResult)

    return finalResult
  } catch (error) {
    aiLogger.logError(logContext, error)

    // Don't provide fallback - let the caller handle the error
    throw error
  }
}
