import { createClient } from '@/lib/supabase/server'
import { STORAGE_CONFIG } from './config'
import { generateFileName, validateFile, type FileInfo } from './validation'

export interface UploadUrlResponse {
  uploadUrl: string
  filePath: string
  publicUrl: string
}

export interface UploadError {
  error: string
}

/**
 * Generate signed URL for file upload
 */
export async function generateUploadUrl(
  userId: string,
  fileInfo: FileInfo
): Promise<UploadUrlResponse | UploadError> {
  try {
    // Validate file
    const validation = validateFile(fileInfo)
    if (!validation.isValid) {
      return { error: validation.error! }
    }

    const supabase = await createClient()
    
    // Generate unique file path
    const filePath = generateFileName(userId, fileInfo.name)
    
    // Create signed URL for upload
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from(STORAGE_CONFIG.BUCKET_NAME)
      .createSignedUploadUrl(filePath, {
        upsert: false
      })

    if (uploadError) {
      console.error('Error creating signed upload URL:', uploadError)
      return { error: 'Не удалось создать URL для загрузки файла' }
    }

    // Get public URL for the file
    const { data: publicUrlData } = supabase.storage
      .from(STORAGE_CONFIG.BUCKET_NAME)
      .getPublicUrl(filePath)

    return {
      uploadUrl: uploadData.signedUrl,
      filePath,
      publicUrl: publicUrlData.publicUrl
    }
  } catch (error) {
    console.error('Unexpected error generating upload URL:', error)
    return { error: 'Произошла неожиданная ошибка при подготовке загрузки' }
  }
}

/**
 * Delete file from storage
 */
export async function deleteFile(filePath: string): Promise<boolean> {
  try {
    const supabase = await createClient()
    
    const { error } = await supabase.storage
      .from(STORAGE_CONFIG.BUCKET_NAME)
      .remove([filePath])

    if (error) {
      console.error('Error deleting file:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Unexpected error deleting file:', error)
    return false
  }
}

/**
 * Extract file path from public URL
 */
export function extractFilePathFromUrl(publicUrl: string): string | null {
  try {
    const url = new URL(publicUrl)
    const pathParts = url.pathname.split('/')
    const bucketIndex = pathParts.findIndex(part => part === STORAGE_CONFIG.BUCKET_NAME)
    
    if (bucketIndex === -1 || bucketIndex === pathParts.length - 1) {
      return null
    }
    
    return pathParts.slice(bucketIndex + 1).join('/')
  } catch {
    return null
  }
}
