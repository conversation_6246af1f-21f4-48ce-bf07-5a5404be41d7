import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>c<PERSON>, Trash2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertCircle } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"
import { Note } from "@/types/notes"
import { TagsSkeleton } from "./TagsSkeleton"
import { NoteImage } from "./NoteImage"

interface NotesListProps {
  notes: Note[]
  onTagClick: (tag: string) => void
  onEditClick: (note: Note) => void
  onDeleteClick: (noteId: string) => void
  onReanalyzeClick: (noteId: string) => void
  userEmail?: string
}

export function NotesList({ notes, onTagClick, onEditClick, onD<PERSON>te<PERSON>lick, onReanalyzeClick, userEmail }: NotesListProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("ru-RU", {
      day: "numeric",
      month: "short",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  if (notes.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">Заметки не найдены</h3>
          <p className="text-muted-foreground">
            Создайте свою первую заметку или измените критерии поиска
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {notes.map((note) => (
        <Card key={note.id} className="hover:shadow-md transition-shadow gap-1">
          <CardHeader className="pb-1">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Avatar className="h-8 w-8">
                  <AvatarFallback>{userEmail?.[0]?.toUpperCase() || 'U'}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="text-sm font-medium">Вы</div>
                  <div className="text-xs text-muted-foreground">
                    {formatDate(note.created_at)}
                    {note.updated_at !== note.created_at && (
                      <span className="text-muted-foreground/70"> • изменено</span>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {note.content_type === "file" && (
                  <Badge variant="outline">
                    <Paperclip className="h-3 w-3 mr-1" />
                    Файл
                  </Badge>
                )}
                {note.aiAnalysisFailed && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onReanalyzeClick(note.id)}
                    className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                    title="Повторить AI анализ"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEditClick(note)}
                  className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                >
                  <Edit3 className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDeleteClick(note.id)}
                  className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>

          <CardContent className="pt-0">
            <div className="space-y-3">
              {/* Прикрепленное изображение */}
              {note.attachment_url && (
                <NoteImage attachmentUrl={note.attachment_url} />
              )}

              {/* Содержимое заметки */}
              {note.content && (
                note.content_type === "file" ? (
                  <div className="flex items-center space-x-2 p-3 bg-muted rounded-lg">
                    <FileText className="h-5 w-5 text-muted-foreground" />
                    <span className="font-medium">{note.content}</span>
                  </div>
                ) : (
                  <div className="prose max-w-none">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>{note.content}</ReactMarkdown>
                  </div>
                )
              )}

              {/* Теги и статус AI анализа */}
              {(note.tags.length > 0 || note.isAnalyzing || note.aiAnalysisFailed) && (
                <>
                  <Separator />
                  {note.isAnalyzing ? (
                    <TagsSkeleton />
                  ) : note.aiAnalysisFailed ? (
                    <div className="flex items-center gap-2 text-muted-foreground text-sm">
                      <AlertCircle className="h-4 w-4 text-orange-500" />
                      <span>AI анализ не удался</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onReanalyzeClick(note.id)}
                        className="h-6 text-xs"
                      >
                        <RefreshCw className="h-3 w-3 mr-1" />
                        Повторить
                      </Button>
                    </div>
                  ) : (
                    <div className="flex flex-wrap gap-1">
                      {note.tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="text-xs cursor-pointer hover:bg-secondary/80"
                          onClick={() => onTagClick(tag)}
                        >
                          <Hash className="h-2 w-2 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                </>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
