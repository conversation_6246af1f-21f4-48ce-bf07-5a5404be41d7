import { useState, useRef } from "react"
import { Send, Edit3, <PERSON>clip, ImageIcon, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"

interface NoteComposerProps {
  onSubmit: (content: string, attachmentUrl?: string) => Promise<void>
  userEmail?: string
  isSubmitting?: boolean
}

export function NoteComposer({ onSubmit, userEmail, isSubmitting = false }: NoteComposerProps) {
  const [content, setContent] = useState("")
  const [isMarkdownMode, setIsMarkdownMode] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadingFile, setUploadingFile] = useState(false)
  const [filePreviewUrl, setFilePreviewUrl] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Пожалуйста, выберите изображение')
      return
    }

    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert('Файл слишком большой. Максимальный размер: 10MB')
      return
    }

    setSelectedFile(file)

    // Create preview URL
    const previewUrl = URL.createObjectURL(file)
    setFilePreviewUrl(previewUrl)
  }

  const handleRemoveFile = () => {
    setSelectedFile(null)
    if (filePreviewUrl) {
      URL.revokeObjectURL(filePreviewUrl)
      setFilePreviewUrl(null)
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const uploadFile = async (file: File): Promise<string | null> => {
    try {
      setUploadingFile(true)

      // Get upload URL
      const uploadResponse = await fetch('/api/notes/upload-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type
        })
      })

      if (!uploadResponse.ok) {
        const error = await uploadResponse.json()
        throw new Error(error.error || 'Failed to get upload URL')
      }

      const { uploadUrl, publicUrl } = await uploadResponse.json()

      // Upload file to Supabase Storage
      const uploadFileResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type
        }
      })

      if (!uploadFileResponse.ok) {
        throw new Error('Failed to upload file')
      }

      return publicUrl
    } catch (error) {
      console.error('File upload error:', error)
      alert('Ошибка загрузки файла: ' + (error instanceof Error ? error.message : 'Неизвестная ошибка'))
      return null
    } finally {
      setUploadingFile(false)
    }
  }

  const handleSubmit = async () => {
    if ((!content.trim() && !selectedFile) || submitting || isSubmitting || uploadingFile) return

    setSubmitting(true)
    try {
      let attachmentUrl: string | undefined

      // Upload file if selected
      if (selectedFile) {
        attachmentUrl = await uploadFile(selectedFile) || undefined
        if (selectedFile && !attachmentUrl) {
          // File upload failed, don't proceed
          return
        }
      }

      await onSubmit(content.trim(), attachmentUrl)
      setContent("")
      setIsMarkdownMode(false)
      handleRemoveFile()
    } finally {
      setSubmitting(false)
    }
  }

  const isDisabled = (!content.trim() && !selectedFile) || submitting || isSubmitting || uploadingFile

  return (
    <Card className="py-1">
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarFallback>{userEmail?.[0]?.toUpperCase() || 'U'}</AvatarFallback>
              </Avatar>
              <span className="text-sm font-medium">Новая заметка</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMarkdownMode(!isMarkdownMode)}
              className={isMarkdownMode ? "bg-muted" : ""}
            >
              <Edit3 className="h-4 w-4 mr-1" />
              Markdown
            </Button>
          </div>

          <Textarea
            placeholder={isMarkdownMode ? "Введите текст с поддержкой Markdown..." : "Введите вашу заметку..."}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            className="min-h-[100px] resize-none"
          />

          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />

          {/* File preview */}
          {selectedFile && filePreviewUrl && (
            <div className="border rounded-md p-3 bg-muted/50">
              <div className="flex items-center justify-between mb-2">
                <div className="text-xs text-muted-foreground">Прикрепленное изображение:</div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRemoveFile}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
              <div className="relative">
                <img
                  src={filePreviewUrl}
                  alt="Preview"
                  className="max-w-full max-h-48 rounded object-contain"
                />
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                {selectedFile.name} ({Math.round(selectedFile.size / 1024)} KB)
              </div>
            </div>
          )}

          {isMarkdownMode && content && (
            <div className="border rounded-md p-3 bg-muted/50">
              <div className="text-xs text-muted-foreground mb-2">Предварительный просмотр:</div>
              <div className="prose max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {content}
                </ReactMarkdown>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" disabled>
                <Paperclip className="h-4 w-4 mr-1" />
                Файл
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                disabled={uploadingFile}
              >
                <ImageIcon className="h-4 w-4 mr-1" />
                Изображение
              </Button>
            </div>
            <Button onClick={handleSubmit} disabled={isDisabled}>
              <Send className="h-4 w-4 mr-1" />
              {uploadingFile ? 'Загрузка...' : (submitting || isSubmitting) ? 'Сохранение...' : 'Отправить'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
