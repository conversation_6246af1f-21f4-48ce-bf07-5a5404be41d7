export interface Note {
  id: string
  content: string
  content_type: 'text' | 'file' | 'link'
  tags: string[]
  created_at: string
  updated_at: string
  summary_ai: string | null
  attachment_url: string | null
  isAnalyzing?: boolean
  aiAnalysisFailed?: boolean
}

export interface CreateNoteRequest {
  content: string
  content_type?: 'text' | 'file' | 'link'
  attachment_url?: string
}

export interface NotesResponse {
  notes: Note[]
}

export interface CreateNoteResponse {
  note: Note
}

export interface UpdateNoteRequest {
  content: string
}

export interface UpdateNoteResponse {
  note: Note
}

export interface TagsResponse {
  tags: string[]
}
